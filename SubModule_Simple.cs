using System;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Library;

namespace LAN_Fight
{
    /// <summary>
    /// 简化版的SubModule，用于测试基础功能
    /// </summary>
    public class SubModule_Simple : MBSubModuleBase
    {
        /// <summary>
        /// 模组加载时调用
        /// </summary>
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();
            
            try
            {
                // 显示简单的加载消息
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight 模组已加载（简化版）", Colors.Green));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight 模组加载失败: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 模组卸载时调用
        /// </summary>
        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();
            
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight 模组已卸载", Colors.Yellow));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight 模组卸载时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
