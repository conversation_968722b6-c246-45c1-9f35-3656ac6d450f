using System;
using HarmonyLib;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;

namespace LAN_Fight.Patches
{
    /// <summary>
    /// 主菜单补丁，显示模组加载状态
    /// </summary>
    [HarmonyPatch]
    public static class MainMenuPatch
    {
        private static bool _hasShownStatus = false;

        /// <summary>
        /// 拦截MBSubModuleBase的OnSubModuleLoad，确保在模组加载后显示状态
        /// </summary>
        [HarmonyPostfix]
        [HarmonyPatch(typeof(MBSubModuleBase), "OnSubModuleLoad")]
        public static void MBSubModuleBase_OnSubModuleLoad_Postfix(MBSubModuleBase __instance)
        {
            try
            {
                // 只处理我们自己的模组
                if (__instance.GetType().Name == "SubModule" && __instance.GetType().Namespace == "LAN_Fight")
                {
                    ShowModStatus();
                    _hasShownStatus = true;
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"MainMenuPatch显示模组状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示模组状态
        /// </summary>
        private static void ShowModStatus()
        {
            try
            {
                // 在控制台显示详细状态
                Debug.Print("", 0, Debug.DebugColor.White);
                Debug.Print("=== LAN Fight 联机模组已生效 ===", 0, Debug.DebugColor.Green);
                Debug.Print($"版本: {ModGlobals.MOD_VERSION}", 0, Debug.DebugColor.White);
                Debug.Print("状态: 已加载并运行", 0, Debug.DebugColor.Green);
                Debug.Print("", 0, Debug.DebugColor.White);

                // 在游戏内显示简短消息
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight 联机模组已生效", Colors.Green));

                // 显示使用提示
                InformationManager.DisplayMessage(new InformationMessage("按 ~ 键打开控制台，输入 lan_help 查看命令", Colors.Cyan));
            }
            catch (Exception ex)
            {
                Debug.Print($"显示模组状态时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查模组状态并显示详细信息
        /// </summary>
        public static void ShowDetailedModStatus()
        {
            try
            {
                Debug.Print("=== LAN Fight 模组详细状态 ===", 0, Debug.DebugColor.Cyan);
                Debug.Print($"模组版本: {ModGlobals.MOD_VERSION}", 0, Debug.DebugColor.White);
                Debug.Print($"网络管理器: {(ModGlobals.NetworkManager != null ? "已初始化" : "未初始化")}", 0, Debug.DebugColor.White);
                Debug.Print($"副将管理器: {(ModGlobals.HeroManager != null ? "已初始化" : "未初始化")}", 0, Debug.DebugColor.White);
                Debug.Print($"指派管理器: {(ModGlobals.AssignmentManager != null ? "已初始化" : "未初始化")}", 0, Debug.DebugColor.White);
                Debug.Print($"命令处理器: {(ModGlobals.CommandInputHandler != null ? "已初始化" : "未初始化")}", 0, Debug.DebugColor.White);
                Debug.Print($"初始化状态: {(ModGlobals.IsInitialized ? "已完成" : "未完成")}", 0, Debug.DebugColor.White);
                
                // 也在游戏内显示
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight 模组状态已输出到控制台", Colors.Cyan));
            }
            catch (Exception ex)
            {
                Debug.Print($"显示详细模组状态时出错: {ex.Message}");
            }
        }
    }
}
