using System;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Library;
using HarmonyLib;

namespace LAN_Fight
{
    /// <summary>
    /// LAN_Fight模组的主入口点
    /// 负责初始化所有模块、应用Harmony补丁、驱动网络事件轮询
    /// </summary>
    public class SubModule : MBSubModuleBase
    {
        private Harmony _harmony;

        /// <summary>
        /// 模组加载时调用
        /// </summary>
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();

            try
            {
                // 初始化Harmony
                _harmony = new Harmony("lan_fight.mod");
                _harmony.PatchAll();

                // 初始化全局管理器
                ModGlobals.Initialize();

                InformationManager.DisplayMessage(new InformationMessage("LAN Fight模组已加载", Colors.Green));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组加载失败: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 模组卸载时调用
        /// </summary>
        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();

            try
            {
                // 清理资源
                ModGlobals.Cleanup();

                // 移除Harmony补丁
                _harmony?.UnpatchAll("lan_fight.mod");
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组卸载时出错: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 每帧调用
        /// </summary>
        protected override void OnApplicationTick(float dt)
        {
            base.OnApplicationTick(dt);

            try
            {
                // 更新网络管理器
                ModGlobals.NetworkManager?.Update(dt);

                // 更新命令输入处理器
                ModGlobals.CommandInputHandler?.Update();
            }
            catch (Exception)
            {
                // 静默处理，避免频繁错误消息
            }
        }
    }
}
