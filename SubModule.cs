﻿using System;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Library;
using HarmonyLib;

namespace LAN_Fight
{
    /// <summary>
    /// LAN_Fight模组的主入口点
    /// 负责初始化所有模块、应用Harmony补丁、驱动网络事件轮询
    /// </summary>
    public class SubModule : MBSubModuleBase
    {
        private Harmony _harmony;

        /// <summary>
        /// 模组加载时调用
        /// </summary>
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();

            try
            {
                // 初始化Harmony
                _harmony = new Harmony("lan_fight.mod");
                _harmony.PatchAll();

                // 初始化全局管理器
                ModGlobals.Initialize();

                // 显示模组状态
                ShowModStatus();
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组加载失败: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 模组卸载时调用
        /// </summary>
        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();

            try
            {
                // 清理资源
                ModGlobals.Cleanup();

                // 移除Harmony补丁
                _harmony?.UnpatchAll("lan_fight.mod");
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组卸载时出错: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 每帧调用
        /// </summary>
        protected override void OnApplicationTick(float dt)
        {
            base.OnApplicationTick(dt);

            try
            {
                // 更新网络管理器
                ModGlobals.NetworkManager?.Update(dt);

                // 更新命令输入处理器
                ModGlobals.CommandInputHandler?.Update();
            }
            catch (Exception)
            {
                // 静默处理，避免频繁错误消息
            }
        }

        /// <summary>
        /// 显示模组状态信息
        /// </summary>
        private void ShowModStatus()
        {
            try
            {
                // 在控制台显示详细状态
                InformationManager.DisplayMessage(new InformationMessage("", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 联机模组已生效 ===", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage($"版本: {ModGlobals.MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("状态: 已加载并运行", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("", Colors.White));

                // 显示使用提示
                InformationManager.DisplayMessage(new InformationMessage("按 ~ 键打开控制台，输入 lan_help 查看命令", Colors.Cyan));

                // 也在Debug控制台输出
                System.Diagnostics.Debug.WriteLine("=== LAN Fight 联机模组已生效 ===");
                System.Diagnostics.Debug.WriteLine($"版本: {ModGlobals.MOD_VERSION}");
                System.Diagnostics.Debug.WriteLine("状态: 已加载并运行");
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示模组状态时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
