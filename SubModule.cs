﻿using System;
using System.Collections.Generic;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Library;
using static TaleWorlds.Library.CommandLineFunctionality;

namespace LAN_Fight
{
    /// <summary>
    /// LAN_Fight模组的主入口点 - 简化版本
    /// 专注于基础功能，确保稳定性
    /// </summary>
    public class SubModule : MBSubModuleBase
    {
        public const string MOD_VERSION = "1.0.0";
        private static bool _isInitialized = false;

        /// <summary>
        /// 模组加载时调用
        /// </summary>
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();

            try
            {
                if (_isInitialized)
                    return;

                // 显示模组加载状态
                ShowModStatus();

                // 手动注册命令
                RegisterCommands();

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组加载失败: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 模组卸载时调用
        /// </summary>
        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();

            try
            {
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight模组已卸载", Colors.Yellow));
                _isInitialized = false;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组卸载时出错: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 手动注册命令到控制台系统
        /// </summary>
        private void RegisterCommands()
        {
            try
            {
                // 注册测试命令
                AddCommand("test", "lan_fight", "测试模组功能", TestCommand);
                AddCommand("help", "lan_fight", "显示帮助信息", HelpCommand);
                AddCommand("info", "lan_fight", "显示模组信息", InfoCommand);
                AddCommand("version", "lan_fight", "显示版本信息", VersionCommand);

                InformationManager.DisplayMessage(new InformationMessage("控制台命令已注册", Colors.Green));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"注册命令时出错: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 测试命令
        /// </summary>
        private string TestCommand(List<string> args)
        {
            InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组测试 ===", Colors.Yellow));
            InformationManager.DisplayMessage(new InformationMessage("✓ 模组已正确加载", Colors.Green));
            InformationManager.DisplayMessage(new InformationMessage("✓ 控制台命令系统正常", Colors.Green));
            InformationManager.DisplayMessage(new InformationMessage("✓ 消息显示系统正常", Colors.Green));
            return "模组测试完成 - 所有基础功能正常";
        }

        /// <summary>
        /// 帮助命令
        /// </summary>
        private string HelpCommand(List<string> args)
        {
            InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 命令帮助 ===", Colors.Cyan));
            InformationManager.DisplayMessage(new InformationMessage("help.lan_fight - 显示此帮助", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("test.lan_fight - 测试模组功能", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("info.lan_fight - 显示模组信息", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("version.lan_fight - 显示版本信息", Colors.White));
            return "LAN Fight 帮助信息已显示";
        }

        /// <summary>
        /// 信息命令
        /// </summary>
        private string InfoCommand(List<string> args)
        {
            InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组信息 ===", Colors.Cyan));
            InformationManager.DisplayMessage(new InformationMessage($"版本: {MOD_VERSION}", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("类型: 局域网多人联机模组", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("状态: 基础版本", Colors.White));
            return "模组信息已显示";
        }

        /// <summary>
        /// 版本命令
        /// </summary>
        private string VersionCommand(List<string> args)
        {
            string versionInfo = $"LAN Fight v{MOD_VERSION}";
            InformationManager.DisplayMessage(new InformationMessage(versionInfo, Colors.Green));
            return versionInfo;
        }

        /// <summary>
        /// 显示模组状态信息
        /// </summary>
        private void ShowModStatus()
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 联机模组已生效 ===", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage($"版本: {MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("状态: 基础版本已加载", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("按 Alt+~ 打开控制台，输入 help.lan_fight 查看命令", Colors.Cyan));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示模组状态时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
