﻿using System;
using System.Collections.Generic;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Library;
using static TaleWorlds.Library.CommandLineFunctionality;

namespace LAN_Fight
{
    /// <summary>
    /// LAN_Fight模组的主入口点 - 简化版本
    /// 专注于基础功能，确保稳定性
    /// </summary>
    public class SubModule : MBSubModuleBase
    {
        public const string MOD_VERSION = "1.0.0";
        private static bool _isInitialized = false;

        /// <summary>
        /// 模组加载时调用
        /// </summary>
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();

            try
            {
                if (_isInitialized)
                    return;

                // 显示模组加载状态
                ShowModStatus();

                // 手动注册命令
                RegisterCommands();

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组加载失败: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 模组卸载时调用
        /// </summary>
        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();

            try
            {
                InformationManager.DisplayMessage(new InformationMessage("LAN Fight模组已卸载", Colors.Yellow));
                _isInitialized = false;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"LAN Fight模组卸载时出错: {ex.Message}", Colors.Red));
            }
        }

        /// <summary>
        /// 手动注册命令到控制台系统
        /// </summary>
        private void RegisterCommands()
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("正在注册控制台命令...", Colors.Yellow));

                // 注意：RGL控制台命令通过属性自动注册
                // 这里我们只是显示注册状态
                InformationManager.DisplayMessage(new InformationMessage("控制台命令已准备就绪", Colors.Green));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"准备命令时出错: {ex.Message}", Colors.Red));
            }
        }



        /// <summary>
        /// 显示模组状态信息
        /// </summary>
        private void ShowModStatus()
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 联机模组已生效 ===", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage($"版本: {MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("状态: 基础版本已加载", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("按 Alt+~ 打开控制台，输入 help.lan_fight 查看命令", Colors.Cyan));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示模组状态时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
