using System.Collections.Generic;
using TaleWorlds.Library;
using static TaleWorlds.Library.CommandLineFunctionality;

namespace LAN_Fight.Commands
{
    /// <summary>
    /// LAN Fight 控制台命令
    /// 使用 CommandLineArgumentFunction 属性注册到RGL控制台系统
    /// </summary>
    public static class LANFightCommands
    {
        /// <summary>
        /// 显示帮助信息
        /// 使用方法: help.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("help", "lan_fight")]
        public static string ShowHelp(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 命令帮助 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage("help.lan_fight - 显示此帮助", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("host.lan_fight - 启动服务器", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("status.lan_fight - 显示状态", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("disconnect.lan_fight - 断开连接", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("connect.lan_fight <IP> - 连接到服务器", Colors.White));
                
                return "LAN Fight 帮助信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"显示帮助时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 启动服务器
        /// 使用方法: host.lan_fight [端口]
        /// </summary>
        [CommandLineArgumentFunction("host", "lan_fight")]
        public static string StartServer(List<string> args)
        {
            try
            {
                int port = ModGlobals.DEFAULT_PORT;
                
                // 解析端口参数
                if (args.Count > 0 && int.TryParse(args[0], out int customPort))
                {
                    if (customPort >= 1024 && customPort <= 65535)
                    {
                        port = customPort;
                    }
                    else
                    {
                        return "端口号必须在1024-65535之间";
                    }
                }

                // 检查是否已经在运行
                if (ModGlobals.NetworkManager?.IsRunning == true)
                {
                    return "网络服务已在运行中";
                }

                // 启动服务器
                bool success = ModGlobals.NetworkManager?.StartServer(port) ?? false;
                
                if (success)
                {
                    return $"服务器已启动，端口: {port}";
                }
                else
                {
                    return "服务器启动失败";
                }
            }
            catch (System.Exception ex)
            {
                return $"启动服务器时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 连接到服务器
        /// 使用方法: connect.lan_fight <IP地址> [端口]
        /// </summary>
        [CommandLineArgumentFunction("connect", "lan_fight")]
        public static string ConnectToServer(List<string> args)
        {
            try
            {
                if (args.Count == 0)
                {
                    return "用法: connect.lan_fight <IP地址> [端口]";
                }

                string address = args[0];
                int port = ModGlobals.DEFAULT_PORT;

                // 解析端口参数
                if (args.Count > 1 && int.TryParse(args[1], out int customPort))
                {
                    if (customPort >= 1024 && customPort <= 65535)
                    {
                        port = customPort;
                    }
                    else
                    {
                        return "端口号必须在1024-65535之间";
                    }
                }

                // 检查是否已经在运行
                if (ModGlobals.NetworkManager?.IsRunning == true)
                {
                    return "网络服务已在运行中，请先断开连接";
                }

                // 连接到服务器
                bool success = ModGlobals.NetworkManager?.ConnectToServer(address, port) ?? false;
                
                if (success)
                {
                    return $"正在连接到服务器: {address}:{port}";
                }
                else
                {
                    return "连接服务器失败";
                }
            }
            catch (System.Exception ex)
            {
                return $"连接服务器时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示当前状态
        /// 使用方法: status.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("status", "lan_fight")]
        public static string ShowStatus(List<string> args)
        {
            try
            {
                var networkManager = ModGlobals.NetworkManager;
                var assignmentManager = ModGlobals.AssignmentManager;
                var heroManager = ModGlobals.HeroManager;

                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 状态 ===", Colors.Cyan));
                
                // 网络状态
                if (networkManager?.IsRunning == true)
                {
                    if (ModGlobals.IsServer)
                    {
                        InformationManager.DisplayMessage(new InformationMessage($"模式: 服务器 (连接数: {networkManager.ConnectedClientsCount})", Colors.Green));
                    }
                    else if (ModGlobals.IsClient)
                    {
                        InformationManager.DisplayMessage(new InformationMessage("模式: 客户端 (已连接)", Colors.Green));
                    }
                }
                else
                {
                    InformationManager.DisplayMessage(new InformationMessage("状态: 未连接", Colors.Yellow));
                }

                // 副将状态
                if (heroManager != null)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"副将数量: {heroManager.HeroCount}", Colors.White));
                }

                // 玩家状态
                if (assignmentManager != null)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"连接玩家: {assignmentManager.ConnectedPlayerCount}, 已指派: {assignmentManager.AssignedPlayerCount}", Colors.White));
                }

                return "状态信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"获取状态时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 断开连接
        /// 使用方法: disconnect.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("disconnect", "lan_fight")]
        public static string Disconnect(List<string> args)
        {
            try
            {
                if (ModGlobals.NetworkManager?.IsRunning != true)
                {
                    return "当前没有网络连接";
                }

                if (ModGlobals.IsServer)
                {
                    ModGlobals.NetworkManager.StopServer();
                    return "服务器已停止";
                }
                else if (ModGlobals.IsClient)
                {
                    ModGlobals.NetworkManager.DisconnectFromServer();
                    return "已断开与服务器的连接";
                }

                return "未知的连接状态";
            }
            catch (System.Exception ex)
            {
                return $"断开连接时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示详细的模组信息
        /// 使用方法: info.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("info", "lan_fight")]
        public static string ShowModInfo(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组信息 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage($"版本: {ModGlobals.MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"网络管理器: {(ModGlobals.NetworkManager != null ? "已初始化" : "未初始化")}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"副将管理器: {(ModGlobals.HeroManager != null ? "已初始化" : "未初始化")}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"指派管理器: {(ModGlobals.AssignmentManager != null ? "已初始化" : "未初始化")}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"命令处理器: {(ModGlobals.CommandInputHandler != null ? "已初始化" : "未初始化")}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"初始化状态: {(ModGlobals.IsInitialized ? "已完成" : "未完成")}", Colors.White));

                return "模组信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"显示模组信息时出错: {ex.Message}";
            }
        }
    }
}
