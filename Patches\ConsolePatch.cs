using System;
using HarmonyLib;
using TaleWorlds.Library;

namespace LAN_Fight.Patches
{
    /// <summary>
    /// 控制台补丁，拦截开发者控制台命令
    /// </summary>
    [HarmonyPatch]
    public static class ConsolePatch
    {
        /// <summary>
        /// 拦截控制台命令执行
        /// 这个补丁会尝试拦截游戏的控制台命令系统
        /// </summary>
        [HarmonyPrefix]
        [HarmonyPatch(typeof(Debug), "Print")]
        public static bool Debug_Print_Prefix(string message, int logLevel = 0, Debug.DebugColor color = Debug.DebugColor.White, ulong debugFilter = 17592186044416UL)
        {
            try
            {
                // 检查是否为我们的命令
                if (message != null && message.StartsWith("lan_"))
                {
                    ProcessConsoleCommand(message);
                    return false; // 阻止原始消息显示
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"控制台命令处理出错: {ex.Message}", Colors.Red));
            }
            
            return true; // 允许原始消息继续处理
        }
        
        /// <summary>
        /// 处理控制台命令
        /// </summary>
        /// <param name="command">命令文本</param>
        private static void ProcessConsoleCommand(string command)
        {
            try
            {
                // 移除 "lan_" 前缀
                string actualCommand = command.Substring(4);
                
                InformationManager.DisplayMessage(new InformationMessage($"执行LAN Fight命令: {actualCommand}", Colors.Green));
                
                // 调用ChatPatch中的命令处理逻辑
                ChatPatch.ProcessCommandDirect("/" + actualCommand);
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行控制台命令时出错: {ex.Message}", Colors.Red));
            }
        }
        
        /// <summary>
        /// 显示控制台命令帮助
        /// </summary>
        public static void ShowConsoleHelp()
        {
            InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 控制台命令 ===", Colors.Cyan));
            InformationManager.DisplayMessage(new InformationMessage("在开发者控制台中输入以下命令：", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("lan_help - 显示帮助", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("lan_host - 启动服务器", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("lan_status - 显示状态", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("lan_disconnect - 断开连接", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("按 ~ 键打开控制台，或使用 Ctrl+~ 组合键", Colors.Yellow));
        }
    }
}
