   .winmd.dll.exe    'D:\VS Project\LAN_Fight\packages.config(D:\VS Project\LAN_Fight\lib\0Harmony.dll*D:\VS Project\LAN_Fight\lib\LiteNetLib.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Microsoft.CSharp.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\mscorlib.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dllmD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.dllcD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Core.dlleD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Engine.dllfD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Library.dllkD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Localization.dlllD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.dllkD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ObjectSystem.dllkD:\Steam\steamapps\common\Mount & Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ScreenSystem.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\netstandard.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}&D:\VS Project\LAN_Fight\bin\x64\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}OD:\VS Project\LAN_Fight\obj\x64\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2amd64
v4.0.30319         