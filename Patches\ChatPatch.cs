using System;
using System.Linq;
using HarmonyLib;
using TaleWorlds.Library;
using TaleWorlds.Core;

namespace LAN_Fight.Patches
{
    /// <summary>
    /// 聊天补丁，拦截聊天消息以实现开发者命令
    /// 支持 /host 和 /connect <ip> 命令
    /// </summary>
    [HarmonyPatch]
    public static class ChatPatch
    {
        #region 命令常量
        
        private const string HOST_COMMAND = "/host";
        private const string CONNECT_COMMAND = "/connect";
        private const string DISCONNECT_COMMAND = "/disconnect";
        private const string STATUS_COMMAND = "/status";
        private const string HELP_COMMAND = "/help";
        
        #endregion

        /// <summary>
        /// 拦截聊天消息处理
        /// 注意：这个补丁需要根据实际的Bannerlord聊天系统API来调整
        /// 目前是一个占位符实现
        /// </summary>
        [HarmonyPrefix]
        [HarmonyPatch(typeof(InformationManager), "DisplayMessage")]
        public static bool DisplayMessage_Prefix(InformationMessage message)
        {
            try
            {
                // 检查是否为聊天消息（这里需要根据实际情况调整判断逻辑）
                if (message?.Information != null)
                {
                    string text = message.Information;
                    
                    // 检查是否为命令
                    if (text.StartsWith("/"))
                    {
                        return ProcessCommand(text);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止原始消息显示
                System.Diagnostics.Debug.WriteLine($"ChatPatch处理消息时出错: {ex.Message}");
            }
            
            // 允许原始消息继续处理
            return true;
        }
        
        /// <summary>
        /// 直接处理命令（供外部调用）
        /// </summary>
        /// <param name="command">命令文本</param>
        public static void ProcessCommandDirect(string command)
        {
            ProcessCommand(command);
        }

        /// <summary>
        /// 处理聊天命令
        /// </summary>
        /// <param name="command">命令文本</param>
        /// <returns>是否阻止原始消息显示</returns>
        private static bool ProcessCommand(string command)
        {
            try
            {
                var parts = command.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 0)
                    return true;
                
                var cmd = parts[0].ToLower();
                
                switch (cmd)
                {
                    case HOST_COMMAND:
                        return ProcessHostCommand(parts);
                        
                    case CONNECT_COMMAND:
                        return ProcessConnectCommand(parts);
                        
                    case DISCONNECT_COMMAND:
                        return ProcessDisconnectCommand(parts);
                        
                    case STATUS_COMMAND:
                        return ProcessStatusCommand(parts);
                        
                    case HELP_COMMAND:
                        return ProcessHelpCommand(parts);
                        
                    default:
                        // 不是我们的命令，让原始消息继续处理
                        return true;
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"命令处理出错: {ex.Message}", Colors.Red));
                return false; // 阻止原始错误消息显示
            }
        }
        
        /// <summary>
        /// 处理 /host 命令
        /// </summary>
        private static bool ProcessHostCommand(string[] parts)
        {
            try
            {
                // 检查是否已经在运行
                if (ModGlobals.NetworkManager?.IsRunning == true)
                {
                    InformationManager.DisplayMessage(new InformationMessage("网络服务已在运行中", Colors.Yellow));
                    return false;
                }
                
                // 解析端口参数（可选）
                int port = ModGlobals.DEFAULT_PORT;
                if (parts.Length > 1)
                {
                    if (!int.TryParse(parts[1], out port) || port < 1024 || port > 65535)
                    {
                        InformationManager.DisplayMessage(new InformationMessage("无效的端口号，使用默认端口 " + ModGlobals.DEFAULT_PORT, Colors.Yellow));
                        port = ModGlobals.DEFAULT_PORT;
                    }
                }
                
                // 启动服务器
                bool success = ModGlobals.NetworkManager?.StartServer(port) ?? false;
                
                if (success)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"服务器已启动，端口: {port}", Colors.Green));
                    InformationManager.DisplayMessage(new InformationMessage("其他玩家可以使用 /connect <你的IP地址> 来连接", Colors.Cyan));
                }
                else
                {
                    InformationManager.DisplayMessage(new InformationMessage("服务器启动失败", Colors.Red));
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"启动服务器时出错: {ex.Message}", Colors.Red));
            }
            
            return false; // 阻止原始命令消息显示
        }
        
        /// <summary>
        /// 处理 /connect 命令
        /// </summary>
        private static bool ProcessConnectCommand(string[] parts)
        {
            try
            {
                // 检查参数
                if (parts.Length < 2)
                {
                    InformationManager.DisplayMessage(new InformationMessage("用法: /connect <服务器IP地址> [端口]", Colors.Yellow));
                    return false;
                }
                
                // 检查是否已经在运行
                if (ModGlobals.NetworkManager?.IsRunning == true)
                {
                    InformationManager.DisplayMessage(new InformationMessage("网络服务已在运行中，请先断开连接", Colors.Yellow));
                    return false;
                }
                
                string address = parts[1];
                int port = ModGlobals.DEFAULT_PORT;
                
                // 解析端口参数（可选）
                if (parts.Length > 2)
                {
                    if (!int.TryParse(parts[2], out port) || port < 1024 || port > 65535)
                    {
                        InformationManager.DisplayMessage(new InformationMessage("无效的端口号，使用默认端口 " + ModGlobals.DEFAULT_PORT, Colors.Yellow));
                        port = ModGlobals.DEFAULT_PORT;
                    }
                }
                
                // 连接到服务器
                bool success = ModGlobals.NetworkManager?.ConnectToServer(address, port) ?? false;
                
                if (!success)
                {
                    InformationManager.DisplayMessage(new InformationMessage("连接服务器失败", Colors.Red));
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"连接服务器时出错: {ex.Message}", Colors.Red));
            }
            
            return false; // 阻止原始命令消息显示
        }
        
        /// <summary>
        /// 处理 /disconnect 命令
        /// </summary>
        private static bool ProcessDisconnectCommand(string[] parts)
        {
            try
            {
                if (ModGlobals.NetworkManager?.IsRunning != true)
                {
                    InformationManager.DisplayMessage(new InformationMessage("当前没有网络连接", Colors.Yellow));
                    return false;
                }
                
                if (ModGlobals.IsServer)
                {
                    ModGlobals.NetworkManager.StopServer();
                }
                else if (ModGlobals.IsClient)
                {
                    ModGlobals.NetworkManager.DisconnectFromServer();
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"断开连接时出错: {ex.Message}", Colors.Red));
            }
            
            return false; // 阻止原始命令消息显示
        }
        
        /// <summary>
        /// 处理 /status 命令
        /// </summary>
        private static bool ProcessStatusCommand(string[] parts)
        {
            try
            {
                var networkManager = ModGlobals.NetworkManager;
                var assignmentManager = ModGlobals.AssignmentManager;
                var heroManager = ModGlobals.HeroManager;
                
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 状态 ===", Colors.Cyan));
                
                // 网络状态
                if (networkManager?.IsRunning == true)
                {
                    if (ModGlobals.IsServer)
                    {
                        InformationManager.DisplayMessage(new InformationMessage($"模式: 服务器 (连接数: {networkManager.ConnectedClientsCount})", Colors.Green));
                    }
                    else if (ModGlobals.IsClient)
                    {
                        InformationManager.DisplayMessage(new InformationMessage("模式: 客户端 (已连接)", Colors.Green));
                    }
                }
                else
                {
                    InformationManager.DisplayMessage(new InformationMessage("状态: 未连接", Colors.Yellow));
                }
                
                // 副将状态
                if (heroManager != null)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"副将数量: {heroManager.HeroCount}", Colors.White));
                }
                
                // 玩家状态
                if (assignmentManager != null)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"连接玩家: {assignmentManager.ConnectedPlayerCount}, 已指派: {assignmentManager.AssignedPlayerCount}", Colors.White));
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"获取状态时出错: {ex.Message}", Colors.Red));
            }
            
            return false; // 阻止原始命令消息显示
        }
        
        /// <summary>
        /// 处理 /help 命令
        /// </summary>
        private static bool ProcessHelpCommand(string[] parts)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 命令帮助 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage("/host [端口] - 启动服务器", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("/connect <IP地址> [端口] - 连接到服务器", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("/disconnect - 断开连接", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("/status - 显示当前状态", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("/help - 显示此帮助", Colors.White));
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示帮助时出错: {ex.Message}", Colors.Red));
            }
            
            return false; // 阻止原始命令消息显示
        }
    }
}
