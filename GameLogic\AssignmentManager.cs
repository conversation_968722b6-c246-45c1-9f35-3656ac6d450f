using System;
using System.Collections.Generic;
using System.Linq;
using TaleWorlds.Library;

namespace LAN_Fight.GameLogic
{
    /// <summary>
    /// 指派关系管理器，负责维护玩家与副将的映射关系
    /// </summary>
    public class AssignmentManager : IDisposable
    {
        #region 私有字段
        
        private readonly Dictionary<string, string> _playerHeroAssignments; // PlayerId -> HeroId
        private readonly Dictionary<string, PlayerInfo> _connectedPlayers; // PlayerId -> PlayerInfo
        private bool _isDisposed = false;
        
        #endregion

        #region 事件
        
        /// <summary>
        /// 玩家指派事件
        /// </summary>
        public event Action<string, string> OnPlayerAssigned; // PlayerId, HeroId
        
        /// <summary>
        /// 玩家取消指派事件
        /// </summary>
        public event Action<string> OnPlayerUnassigned; // PlayerId
        
        /// <summary>
        /// 玩家连接事件
        /// </summary>
        public event Action<PlayerInfo> OnPlayerConnected;
        
        /// <summary>
        /// 玩家断开连接事件
        /// </summary>
        public event Action<string> OnPlayerDisconnected; // PlayerId
        
        #endregion

        #region 属性
        
        /// <summary>
        /// 所有连接的玩家
        /// </summary>
        public IReadOnlyDictionary<string, PlayerInfo> ConnectedPlayers => _connectedPlayers;
        
        /// <summary>
        /// 所有指派关系
        /// </summary>
        public IReadOnlyDictionary<string, string> PlayerHeroAssignments => _playerHeroAssignments;
        
        /// <summary>
        /// 连接的玩家数量
        /// </summary>
        public int ConnectedPlayerCount => _connectedPlayers.Count;
        
        /// <summary>
        /// 已指派的玩家数量
        /// </summary>
        public int AssignedPlayerCount => _playerHeroAssignments.Count;
        
        /// <summary>
        /// 未指派的玩家
        /// </summary>
        public IEnumerable<PlayerInfo> UnassignedPlayers => _connectedPlayers.Values.Where(p => !_playerHeroAssignments.ContainsKey(p.PlayerId));
        
        #endregion

        #region 构造函数
        
        public AssignmentManager()
        {
            _playerHeroAssignments = new Dictionary<string, string>();
            _connectedPlayers = new Dictionary<string, PlayerInfo>();
        }
        
        #endregion

        #region 玩家管理方法
        
        /// <summary>
        /// 添加连接的玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="playerName">玩家名称</param>
        /// <param name="connectionInfo">连接信息</param>
        /// <returns>是否添加成功</returns>
        public bool AddPlayer(string playerId, string playerName, string connectionInfo = null)
        {
            if (string.IsNullOrEmpty(playerId) || string.IsNullOrEmpty(playerName))
                return false;
                
            try
            {
                if (_connectedPlayers.ContainsKey(playerId))
                {
                    InformationManager.DisplayMessage(new InformationMessage($"玩家'{playerName}'已存在", Colors.Yellow));
                    return false;
                }
                
                var playerInfo = new PlayerInfo
                {
                    PlayerId = playerId,
                    PlayerName = playerName,
                    ConnectionInfo = connectionInfo,
                    ConnectedTime = DateTime.Now
                };
                
                _connectedPlayers[playerId] = playerInfo;
                OnPlayerConnected?.Invoke(playerInfo);
                
                InformationManager.DisplayMessage(new InformationMessage($"玩家'{playerName}'已连接", Colors.Green));
                return true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"添加玩家时出错: {ex.Message}", Colors.Red));
                return false;
            }
        }
        
        /// <summary>
        /// 移除玩家
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否移除成功</returns>
        public bool RemovePlayer(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return false;
                
            try
            {
                if (!_connectedPlayers.ContainsKey(playerId))
                    return false;
                
                var playerInfo = _connectedPlayers[playerId];
                
                // 如果玩家有指派，先取消指派
                if (_playerHeroAssignments.ContainsKey(playerId))
                {
                    UnassignPlayer(playerId);
                }
                
                _connectedPlayers.Remove(playerId);
                OnPlayerDisconnected?.Invoke(playerId);
                
                InformationManager.DisplayMessage(new InformationMessage($"玩家'{playerInfo.PlayerName}'已断开连接", Colors.Yellow));
                return true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"移除玩家时出错: {ex.Message}", Colors.Red));
                return false;
            }
        }
        
        /// <summary>
        /// 获取玩家信息
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>玩家信息，如果不存在返回null</returns>
        public PlayerInfo GetPlayer(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return null;
                
            _connectedPlayers.TryGetValue(playerId, out PlayerInfo playerInfo);
            return playerInfo;
        }
        
        #endregion

        #region 指派管理方法
        
        /// <summary>
        /// 将玩家指派给副将
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <param name="heroId">副将ID</param>
        /// <returns>是否指派成功</returns>
        public bool AssignPlayer(string playerId, string heroId)
        {
            if (string.IsNullOrEmpty(playerId) || string.IsNullOrEmpty(heroId))
                return false;
                
            try
            {
                // 检查玩家是否存在
                if (!_connectedPlayers.ContainsKey(playerId))
                {
                    InformationManager.DisplayMessage(new InformationMessage("玩家不存在", Colors.Red));
                    return false;
                }
                
                // 检查副将是否存在
                var hero = ModGlobals.HeroManager?.GetCoopHero(heroId);
                if (hero == null)
                {
                    InformationManager.DisplayMessage(new InformationMessage("副将不存在", Colors.Red));
                    return false;
                }
                
                // 检查副将是否已被指派
                if (_playerHeroAssignments.ContainsValue(heroId))
                {
                    var existingPlayer = _playerHeroAssignments.FirstOrDefault(kvp => kvp.Value == heroId).Key;
                    var existingPlayerInfo = GetPlayer(existingPlayer);
                    InformationManager.DisplayMessage(new InformationMessage($"副将已被玩家'{existingPlayerInfo?.PlayerName}'指派", Colors.Yellow));
                    return false;
                }
                
                // 如果玩家已有指派，先取消原指派
                if (_playerHeroAssignments.ContainsKey(playerId))
                {
                    UnassignPlayer(playerId);
                }
                
                // 执行指派
                _playerHeroAssignments[playerId] = heroId;
                
                // 同时更新HeroManager中的指派关系
                ModGlobals.HeroManager?.AssignHeroToPlayer(heroId, playerId);
                
                OnPlayerAssigned?.Invoke(playerId, heroId);
                
                var playerInfo = GetPlayer(playerId);
                InformationManager.DisplayMessage(new InformationMessage($"玩家'{playerInfo?.PlayerName}'已指派给副将'{hero.Name}'", Colors.Green));
                
                return true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"指派玩家时出错: {ex.Message}", Colors.Red));
                return false;
            }
        }
        
        /// <summary>
        /// 取消玩家的指派
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否取消成功</returns>
        public bool UnassignPlayer(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return false;
                
            try
            {
                if (!_playerHeroAssignments.ContainsKey(playerId))
                    return false;
                
                var heroId = _playerHeroAssignments[playerId];
                var hero = ModGlobals.HeroManager?.GetCoopHero(heroId);
                var playerInfo = GetPlayer(playerId);
                
                _playerHeroAssignments.Remove(playerId);
                
                // 同时更新HeroManager中的指派关系
                ModGlobals.HeroManager?.UnassignHero(heroId);
                
                OnPlayerUnassigned?.Invoke(playerId);
                
                InformationManager.DisplayMessage(new InformationMessage($"玩家'{playerInfo?.PlayerName}'的指派已取消", Colors.Yellow));
                
                return true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"取消指派时出错: {ex.Message}", Colors.Red));
                return false;
            }
        }
        
        /// <summary>
        /// 获取玩家指派的副将ID
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>副将ID，如果未指派返回null</returns>
        public string GetPlayerAssignedHero(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return null;
                
            _playerHeroAssignments.TryGetValue(playerId, out string heroId);
            return heroId;
        }
        
        /// <summary>
        /// 获取副将被指派给的玩家ID
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <returns>玩家ID，如果未被指派返回null</returns>
        public string GetHeroAssignedPlayer(string heroId)
        {
            if (string.IsNullOrEmpty(heroId))
                return null;
                
            return _playerHeroAssignments.FirstOrDefault(kvp => kvp.Value == heroId).Key;
        }
        
        /// <summary>
        /// 检查玩家是否已被指派
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否已被指派</returns>
        public bool IsPlayerAssigned(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return false;
                
            return _playerHeroAssignments.ContainsKey(playerId);
        }
        
        #endregion

        #region IDisposable实现
        
        public void Dispose()
        {
            if (_isDisposed)
                return;
                
            try
            {
                // 清理所有指派关系
                var playerIds = _playerHeroAssignments.Keys.ToList();
                foreach (var playerId in playerIds)
                {
                    UnassignPlayer(playerId);
                }
                
                // 清理所有玩家
                var connectedPlayerIds = _connectedPlayers.Keys.ToList();
                foreach (var playerId in connectedPlayerIds)
                {
                    RemovePlayer(playerId);
                }
                
                _playerHeroAssignments.Clear();
                _connectedPlayers.Clear();
                
                _isDisposed = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AssignmentManager释放资源时出错: {ex.Message}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 玩家信息类
    /// </summary>
    public class PlayerInfo
    {
        /// <summary>
        /// 玩家ID
        /// </summary>
        public string PlayerId { get; set; }
        
        /// <summary>
        /// 玩家名称
        /// </summary>
        public string PlayerName { get; set; }
        
        /// <summary>
        /// 连接信息
        /// </summary>
        public string ConnectionInfo { get; set; }
        
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime ConnectedTime { get; set; }
        
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; } = true;
    }
}
