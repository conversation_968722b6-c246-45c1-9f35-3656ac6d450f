using System;
using TaleWorlds.Core;
using TaleWorlds.Engine;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;

namespace LAN_Fight.Core
{
    /// <summary>
    /// 命令输入处理器，通过预设命令快捷键触发
    /// </summary>
    public class CommandInputHandler
    {
        private float _lastKeyPressTime = 0f;
        private const float KEY_PRESS_COOLDOWN = 1f; // 1秒冷却时间，防止重复触发

        /// <summary>
        /// 更新输入处理
        /// </summary>
        public void Update()
        {
            try
            {
                // 检查是否在战役模式中
                if (Campaign.Current == null)
                    return;

                // 更新时间
                _lastKeyPressTime += Time.ApplicationTime;

                // 检查快捷键（使用简单的预设命令）
                CheckQuickCommands();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CommandInputHandler更新时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查快捷命令
        /// </summary>
        private void CheckQuickCommands()
        {
            // 防止频繁触发
            if (Time.ApplicationTime - _lastKeyPressTime < KEY_PRESS_COOLDOWN)
                return;

            // 使用简单的按键检测（这些方法在TaleWorlds.Engine中应该可用）
            try
            {
                // F1 - 显示帮助
                if (Input.IsKeyDown(0x70)) // F1键的虚拟键码
                {
                    ExecuteCommand("help");
                    _lastKeyPressTime = Time.ApplicationTime;
                }
                // F2 - 启动服务器
                else if (Input.IsKeyDown(0x71)) // F2键的虚拟键码
                {
                    ExecuteCommand("host");
                    _lastKeyPressTime = Time.ApplicationTime;
                }
                // F3 - 显示状态
                else if (Input.IsKeyDown(0x72)) // F3键的虚拟键码
                {
                    ExecuteCommand("status");
                    _lastKeyPressTime = Time.ApplicationTime;
                }
                // F4 - 断开连接
                else if (Input.IsKeyDown(0x73)) // F4键的虚拟键码
                {
                    ExecuteCommand("disconnect");
                    _lastKeyPressTime = Time.ApplicationTime;
                }
            }
            catch (Exception)
            {
                // 如果Input方法不可用，显示提示信息
                ShowKeyboardShortcuts();
            }
        }
        
        /// <summary>
        /// 显示键盘快捷键提示
        /// </summary>
        private void ShowKeyboardShortcuts()
        {
            // 只在第一次显示提示
            if (_lastKeyPressTime == 0f)
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 快捷键 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage("F1 - 显示帮助", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("F2 - 启动服务器", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("F3 - 显示状态", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("F4 - 断开连接", Colors.White));
                _lastKeyPressTime = Time.ApplicationTime;
            }
        }
        
        /// <summary>
        /// 执行命令
        /// </summary>
        private void ExecuteCommand(string command)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令: {command}", Colors.Green));

                // 确保命令以/开头
                if (!command.StartsWith("/"))
                {
                    command = "/" + command;
                }

                // 调用ChatPatch中的命令处理逻辑
                Patches.ChatPatch.ProcessCommandDirect(command);
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
