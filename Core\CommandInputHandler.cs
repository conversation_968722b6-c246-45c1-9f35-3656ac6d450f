using System;
using TaleWorlds.Core;
using TaleWorlds.Engine;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem;

namespace LAN_Fight.Core
{
    /// <summary>
    /// 命令输入处理器，提供简单的命令执行接口
    /// </summary>
    public class CommandInputHandler
    {
        private bool _hasShownWelcome = false;

        /// <summary>
        /// 更新输入处理
        /// </summary>
        public void Update()
        {
            try
            {
                // 检查是否在战役模式中
                if (Campaign.Current == null)
                    return;

                // 显示欢迎信息（只显示一次）
                if (!_hasShownWelcome)
                {
                    ShowWelcomeMessage();
                    _hasShownWelcome = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CommandInputHandler更新时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示欢迎信息
        /// </summary>
        private void ShowWelcomeMessage()
        {
            InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组已加载 ===", Colors.Cyan));
            InformationManager.DisplayMessage(new InformationMessage("当前版本为测试版本，主要功能：", Colors.White));
            InformationManager.DisplayMessage(new InformationMessage("• 基础网络框架已就绪", Colors.Green));
            InformationManager.DisplayMessage(new InformationMessage("• 命令系统已激活", Colors.Green));
            InformationManager.DisplayMessage(new InformationMessage("", Colors.White));

            // 显示控制台命令帮助
            Patches.ConsolePatch.ShowConsoleHelp();
        }
        
        /// <summary>
        /// 公共方法：执行帮助命令
        /// </summary>
        public void ShowHelp()
        {
            ExecuteCommand("help");
        }

        /// <summary>
        /// 公共方法：启动服务器
        /// </summary>
        public void StartServer()
        {
            ExecuteCommand("host");
        }

        /// <summary>
        /// 公共方法：显示状态
        /// </summary>
        public void ShowStatus()
        {
            ExecuteCommand("status");
        }

        /// <summary>
        /// 公共方法：断开连接
        /// </summary>
        public void Disconnect()
        {
            ExecuteCommand("disconnect");
        }
        
        /// <summary>
        /// 执行命令
        /// </summary>
        private void ExecuteCommand(string command)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令: {command}", Colors.Green));

                // 确保命令以/开头
                if (!command.StartsWith("/"))
                {
                    command = "/" + command;
                }

                // 调用ChatPatch中的命令处理逻辑
                Patches.ChatPatch.ProcessCommandDirect(command);
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
