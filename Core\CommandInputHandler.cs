using System;
using TaleWorlds.Core;
using TaleWorlds.Engine;
using TaleWorlds.InputSystem;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;

namespace LAN_Fight.Core
{
    /// <summary>
    /// 命令输入处理器，通过按键触发命令输入
    /// </summary>
    public class CommandInputHandler
    {
        private bool _isInputActive = false;
        private string _currentInput = "";
        
        /// <summary>
        /// 更新输入处理
        /// </summary>
        public void Update()
        {
            try
            {
                // 检查是否在战役模式中
                if (Campaign.Current == null)
                    return;
                
                // 按F12键打开命令输入
                if (Input.IsKeyPressed(InputKey.F12))
                {
                    ToggleCommandInput();
                }
                
                // 如果命令输入激活，处理键盘输入
                if (_isInputActive)
                {
                    HandleKeyboardInput();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CommandInputHandler更新时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 切换命令输入状态
        /// </summary>
        private void ToggleCommandInput()
        {
            _isInputActive = !_isInputActive;
            
            if (_isInputActive)
            {
                _currentInput = "";
                InformationManager.DisplayMessage(new InformationMessage("命令输入模式已激活，输入命令后按回车执行，按ESC取消", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage("当前输入: ", Colors.White));
            }
            else
            {
                InformationManager.DisplayMessage(new InformationMessage("命令输入模式已关闭", Colors.Yellow));
                _currentInput = "";
            }
        }
        
        /// <summary>
        /// 处理键盘输入
        /// </summary>
        private void HandleKeyboardInput()
        {
            try
            {
                // ESC键取消输入
                if (Input.IsKeyPressed(InputKey.Escape))
                {
                    _isInputActive = false;
                    _currentInput = "";
                    InformationManager.DisplayMessage(new InformationMessage("命令输入已取消", Colors.Yellow));
                    return;
                }
                
                // 回车键执行命令
                if (Input.IsKeyPressed(InputKey.Enter) || Input.IsKeyPressed(InputKey.NumpadEnter))
                {
                    if (!string.IsNullOrEmpty(_currentInput))
                    {
                        ExecuteCommand(_currentInput);
                    }
                    _isInputActive = false;
                    _currentInput = "";
                    return;
                }
                
                // 退格键删除字符
                if (Input.IsKeyPressed(InputKey.BackSpace))
                {
                    if (_currentInput.Length > 0)
                    {
                        _currentInput = _currentInput.Substring(0, _currentInput.Length - 1);
                        UpdateInputDisplay();
                    }
                    return;
                }
                
                // 处理字符输入
                HandleCharacterInput();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理键盘输入时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理字符输入
        /// </summary>
        private void HandleCharacterInput()
        {
            // 检查常用字符键
            var keys = new[]
            {
                InputKey.A, InputKey.B, InputKey.C, InputKey.D, InputKey.E, InputKey.F, InputKey.G,
                InputKey.H, InputKey.I, InputKey.J, InputKey.K, InputKey.L, InputKey.M, InputKey.N,
                InputKey.O, InputKey.P, InputKey.Q, InputKey.R, InputKey.S, InputKey.T, InputKey.U,
                InputKey.V, InputKey.W, InputKey.X, InputKey.Y, InputKey.Z,
                InputKey.D0, InputKey.D1, InputKey.D2, InputKey.D3, InputKey.D4,
                InputKey.D5, InputKey.D6, InputKey.D7, InputKey.D8, InputKey.D9,
                InputKey.Period, InputKey.Slash, InputKey.Space
            };
            
            foreach (var key in keys)
            {
                if (Input.IsKeyPressed(key))
                {
                    char character = GetCharacterFromKey(key);
                    if (character != '\0')
                    {
                        _currentInput += character;
                        UpdateInputDisplay();
                    }
                    break;
                }
            }
        }
        
        /// <summary>
        /// 从按键获取字符
        /// </summary>
        private char GetCharacterFromKey(InputKey key)
        {
            switch (key)
            {
                case InputKey.A: return 'a';
                case InputKey.B: return 'b';
                case InputKey.C: return 'c';
                case InputKey.D: return 'd';
                case InputKey.E: return 'e';
                case InputKey.F: return 'f';
                case InputKey.G: return 'g';
                case InputKey.H: return 'h';
                case InputKey.I: return 'i';
                case InputKey.J: return 'j';
                case InputKey.K: return 'k';
                case InputKey.L: return 'l';
                case InputKey.M: return 'm';
                case InputKey.N: return 'n';
                case InputKey.O: return 'o';
                case InputKey.P: return 'p';
                case InputKey.Q: return 'q';
                case InputKey.R: return 'r';
                case InputKey.S: return 's';
                case InputKey.T: return 't';
                case InputKey.U: return 'u';
                case InputKey.V: return 'v';
                case InputKey.W: return 'w';
                case InputKey.X: return 'x';
                case InputKey.Y: return 'y';
                case InputKey.Z: return 'z';
                case InputKey.D0: return '0';
                case InputKey.D1: return '1';
                case InputKey.D2: return '2';
                case InputKey.D3: return '3';
                case InputKey.D4: return '4';
                case InputKey.D5: return '5';
                case InputKey.D6: return '6';
                case InputKey.D7: return '7';
                case InputKey.D8: return '8';
                case InputKey.D9: return '9';
                case InputKey.Period: return '.';
                case InputKey.Slash: return '/';
                case InputKey.Space: return ' ';
                default: return '\0';
            }
        }
        
        /// <summary>
        /// 更新输入显示
        /// </summary>
        private void UpdateInputDisplay()
        {
            InformationManager.DisplayMessage(new InformationMessage($"当前输入: {_currentInput}", Colors.White));
        }
        
        /// <summary>
        /// 执行命令
        /// </summary>
        private void ExecuteCommand(string command)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令: {command}", Colors.Green));
                
                // 调用ChatPatch中的命令处理逻辑
                Patches.ChatPatch.ProcessCommandDirect(command);
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"执行命令时出错: {ex.Message}", Colors.Red));
            }
        }
    }
}
