# LAN Fight - 骑马与砍杀2局域网联机模组

## 模组简介
LAN Fight 是一个为《骑马与砍杀2：霸主》设计的局域网多人联机模组。允许玩家1作为房主和主玩家，其他玩家作为副将参与战斗。

## 安装说明

### 1. 复制模组文件
将整个 `LAN_Fight_Mod` 文件夹复制到您的Bannerlord模组目录：
```
D:\Steam\steamapps\common\Mount & Blade II Bannerlord\Modules\
```

复制后的路径应该是：
```
D:\Steam\steamapps\common\Mount & Blade II Bannerlord\Modules\LAN_Fight_Mod\
```

### 2. 启用模组
1. 启动《骑马与砍杀2：霸主》
2. 在主菜单中点击"模组"
3. 找到"LAN Fight"模组并启用它
4. 重启游戏

### 3. 验证模组是否生效
启动游戏后，您应该看到：
- 游戏内显示绿色消息："LAN Fight 联机模组已生效"
- 控制台输出详细的模组状态信息
- 如果没有看到这些消息，说明模组未正确加载

## 使用方法

### 基本命令
进入战役模式后，使用RGL控制台执行命令：

**控制台命令操作：**
1. 按 **Alt+~** 键打开RGL控制台
2. 输入以下命令：
   - `help.lan_fight` - 显示帮助信息
   - `host.lan_fight [端口]` - 启动服务器（默认端口7777）
   - `connect.lan_fight <IP地址> [端口]` - 连接到服务器
   - `status.lan_fight` - 显示当前连接状态
   - `disconnect.lan_fight` - 断开连接
   - `info.lan_fight` - 显示模组详细信息

**注意：**
- 所有命令都以 `.lan_fight` 结尾
- 使用 Alt+~ 组合键打开RGL控制台（不是普通的~键）
- 命令格式为：`命令名.lan_fight [参数]`

### 房主操作
1. 进入战役模式
2. 按 **Alt+~** 键打开RGL控制台
3. 输入 `host.lan_fight` 启动服务器
4. 告诉其他玩家您的IP地址
5. 等待其他玩家连接

### 客户端操作
1. 进入战役模式
2. 按 **Alt+~** 键打开RGL控制台
3. 输入 `connect.lan_fight <房主IP地址>` 连接到服务器
4. 等待房主指派副将

## 当前版本功能
- ✅ 基础网络连接框架
- ✅ 聊天命令系统
- ✅ 服务器/客户端模式切换
- ⏳ 副将创建和管理（开发中）
- ⏳ 大地图视角同步（开发中）
- ⏳ 战斗控制权转移（开发中）
- ⏳ 管理UI界面（开发中）

## 注意事项
- 当前版本为早期开发版本，主要用于测试基础框架
- 确保所有玩家都在同一局域网内
- 建议使用有线网络连接以获得最佳体验
- 如遇到问题，请检查防火墙设置

## 技术信息
- 支持的游戏版本：Mount & Blade II: Bannerlord v1.2.0+
- 网络库：LiteNetLib
- 注入库：Harmony v2.x
- 最大支持玩家数：8人（1房主+7副将）

## 开发状态
本模组正在积极开发中。当前版本主要实现了基础的网络通信框架和命令系统。

## 版本历史
- v1.0.0 - 初始版本，基础框架和命令系统
