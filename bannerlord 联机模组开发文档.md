
项目开发文档：骑马与砍杀2 - 局域网多人副将联机 (LAN_Fight)

日期： 2025年7月5日
文档状态： 最终版

1. 文档引言
1.1. 文档目的

本文档旨在为“多人副将联机”(LAN_Fight) MOD 的开发团队提供一份全面的技术规范、设计蓝图和实施指南。它将作为整个开发生命周期的核心参考依据，确保团队成员对项目目标、功能、架构和开发过程有一致的理解。

1.2. 项目愿景

为《骑马与砍杀2：霸主》原生的单人沙盒战役模式，引入一个稳定、低延迟、支持小型团队（2-8人）协同作战的局域网（LAN）联机体验。

1.3. 目标用户

希望与一群朋友在同一局域网环境下，共同体验《骑马与砍杀2》史诗般战役的核心玩家群体。

1.4. 核心术语表

房主 (Host): 游戏世界的创建者和唯一权威。负责所有游戏逻辑计算和存档。

客户端 (Client): 以“副将”身份加入房主世界的玩家。不进行逻辑计算，仅作为操作者。

副将 (Sidekick): 游戏内的一个特殊NPC (Hero对象)，可被客户端玩家在战斗中控制。

补丁 (Patch): 使用Harmony库在运行时注入到游戏原生代码中的修改逻辑。

数据包 (Packet): 在网络上传输的、结构化的数据单元。

视图模型 (VM): MVVM设计模式的一部分，UI的数据源和命令处理器。

Agent: 战斗场景中一个可交互单位的实例（如一个士兵、一匹马）。

2. 需求分析
2.1. 功能性需求

FR-100: 联机与会话管理

FR-101 (高): 系统必须支持一个房主和多个客户端（建议最多7个）通过局域网建立连接。

FR-102 (高): 房主必须能通过简单的指令或UI启动服务器模式，加载其本地存档。

FR-103 (高): 客户端必须能通过简单的指令或UI，输入房主的IP地址以加入游戏。

FR-104 (中): 系统必须能处理客户端的正常加入和意外断开，并通知房主。

FR-200: 副将系统管理

FR-201 (高): 房主必须能在游戏内创建一个或多个特殊的“副将”NPC。

FR-202 (高): 当没有客户端玩家控制时，此副将的行为应与普通AI同伴完全一致。

FR-203 (高): 房主必须能通过游戏原生UI管理副将的装备、技能和属性。

FR-204 (高): 系统必须提供一个专用的“联机管理”UI。

FR-205 (高): 在此UI中，房主必须能看到所有已连接的客户端列表和所有可用的副将列表。

FR-206 (高): 房主必须能通过此UI，将一个客户端指派到一个副将席位上，或解除指派。

FR-300: 游戏状态同步

FR-301 (高): 在大地图（战役）模式下，所有客户端的游戏视角必须实时镜像房主的视角。

FR-302 (高): 在大地图模式下，所有客户端的操作输入必须被完全禁用。

FR-303 (高): 当房主进入战斗时，系统必须将每个被指派的副将的控制权，无缝交接给对应的客户端。

FR-304 (高): 在战斗中，客户端必须能完全控制其被指派的副将进行所有战斗操作。

FR-305 (高): 战斗结束后，控制权必须被自动收回，客户端返回至大地图观察者模式。

FR-306 (高): 战斗中，所有客户端必须能看到战场上所有单位（敌我双方）的实时状态。

2.2. 非功能性需求

NFR-1 (性能): 房主端CPU开销增幅应尽可能小。局域网延迟应低于50毫秒。

NFR-2 (稳定性): MOD运行稳定，不能导致游戏崩溃或存档损坏。

NFR-3 (兼容性): 与最新版官方游戏兼容。应声明与其他核心玩法MOD的潜在冲突。

NFR-4 (可维护性): 代码遵循模块化结构，包含清晰注释，易于迭代。

NFR-5 (易用性): 联机和管理流程对非技术用户友好、直观。

3. 系统架构与设计
3.1. 宏观架构

本项目采用**权威主从(Authoritative Host-Client)**的星型网络拓扑。房主作为中心权威服务器，客户端作为哑终端。

3.2. 技术选型

IDE: Visual Studio 2022

反编译器: ILSpy / dotPeek

.NET版本: .NET Framework 4.7.2

注入库: Harmony v2.x

网络库: LiteNetLib

3.3. 项目文件结构

为保证高内聚、低耦合，项目将遵循以下文件结构：

MpSidekickCoop/

Main.cs

Core/

ModGlobals.cs

BattleSyncer.cs

CameraSyncer.cs

ControlHijacker.cs

GameLogic/

CoopHeroManager.cs

AssignmentManager.cs

Network/

NetworkManager.cs

PacketRegistry.cs

Packets.cs

PacketHandlers/

ClientPacketHandler.cs

ServerPacketHandler.cs

Patches/

CampaignTickPatch.cs

ChatPatch.cs

Mission/

MissionLifecyclePatch.cs

AgentSpawnPatch.cs

MissionTickPatch.cs

UI/

UIManager.cs

CoopManagementView.cs

ViewModels/

CoopManagementVM.cs

PlayerEntryVM.cs

HeroSlotVM.cs

4. 模块化实现细则

本章节详细定义了每个文件的职责和实现要点。

4.1. 主程序与核心逻辑

Main.cs: MOD的引导程序。负责初始化所有模块、应用Harmony补丁、驱动网络事件轮询。

Core/ModGlobals.cs: 全局静态容器。定义常量、全局状态、以及对核心管理器的静态引用。

Core/CameraSyncer.cs: 大地图视角同步器。负责获取或应用摄像机状态。

Core/ControlHijacker.cs: (技术核心) 战斗控制权劫持器。负责实现“灵魂附体”的底层逻辑。

Core/BattleSyncer.cs: 战斗状态同步器。负责打包和应用战场上所有单位的状态。

4.2. 游戏玩法逻辑

GameLogic/CoopHeroManager.cs: 副将NPC数据库。负责创建、存储和查询“联机副将”。

GameLogic/AssignmentManager.cs: 指派关系管理器。负责维护玩家与副将的映射关系。

4.3. 网络通信层

Network/NetworkManager.cs: 底层网络管理器。封装LiteNetLib，处理连接生命周期和原始数据包收发。

Network/PacketRegistry.cs: 数据包路由中心。建立数据包类型与其处理逻辑的映射。

Network/Packets.cs: 数据包结构定义。定义所有用于网络通信的数据结构。

Network/PacketHandlers/ClientPacketHandler.cs: 客户端数据包处理器。处理所有从服务器收到的数据包。

Network/PacketHandlers/ServerPacketHandler.cs: 服务器数据包处理器。处理所有从客户端收到的数据包。

4.4. Harmony补丁层

Patches/CampaignTickPatch.cs: 在大地图的每一帧注入代码，以驱动视角同步。

Patches/ChatPatch.cs: 拦截聊天消息，以实现开发者命令。

Patches/Mission/MissionLifecyclePatch.cs: 在战斗开始和结束时注入代码，作为核心逻辑的触发器。

Patches/Mission/AgentSpawnPatch.cs: 在战斗单位生成时注入代码，用于识别副将和阻止客户端生成主角。

Patches/Mission/MissionTickPatch.cs: 在战斗的每一帧注入代码，以驱动战斗状态同步。

4.5. 用户界面层

UI/UIManager.cs: UI层的入口，负责加载和卸载“联机管理”界面。

UI/CoopManagementView.cs: UI的视图层，加载对应的.xml布局文件。

UI/ViewModels/CoopManagementVM.cs: 主管理界面的视图模型，为UI提供数据和命令。

UI/ViewModels/PlayerEntryVM.cs & HeroSlotVM.cs: 定义UI列表中每个条目的数据结构。

5. 核心功能数据流：战斗控制权转移

触发: 房主进入战斗，MissionLifecyclePatch捕获到OnMissionBegin事件。

识别: 房主侧，AgentSpawnPatch在单位生成时，识别出属于被指派副将的Agent。

广播: 战斗初始化完毕后，房主为每个被指派的玩家，创建一个包含其目标Agent.Index的Packet_AssignControl数据包，并定向发送。

拦截: 客户端侧，AgentSpawnPatch已阻止了其本地主角Agent的生成。

接收: 客户端的NetworkManager接收到Packet_AssignControl并分发给处理器。

执行: ClientPacketHandler调用ControlHijacker.Client_TakeControlOfAgent()，传入目标Agent实例。

完成: 控制权转移完成。客户端的输入从此被重定向到指定的副将Agent。

6. 开发路线图与里程碑

里程碑 1: 基础通信框架 (预计1周)

目标: 建立稳定的多客户端连接。

验收标准: 房主可通过/host启动。多个客户端可通过/connect <ip>加入，房主能收到所有连接事件。

里程碑 2: 观察者模式 (预计1周)

目标: 实现大地图视角同步。

验收标准: 所有客户端的屏幕能实时、流畅地镜像房主的战役地图视角。

里程碑 3: 战斗控制权核心 (预计2-3周)

目标: 攻克战斗控制权转移的技术难题。

验收标准: 进入战斗后，被指派的客户端可以控制其副将Agent在场景中自由移动。

里程碑 4: 完整战斗体验 (预计2周)

目标: 实现完整的战斗数据同步。

验收标准: 战斗中所有单位的状态对所有玩家保持同步，战斗体验流畅。

里程碑 5: 游戏逻辑与UI (预计2周)

目标: 实现上层玩法逻辑和管理界面。

验收标准: 房主可以通过一个功能完善的UI界面，创建副将并将其指派给已连接的玩家。

里程碑 6: Alpha测试与交付 (预计1周)

目标: 整合所有功能并进行内部测试。

验收标准: 项目满足所有高优先级的功能性和非功能性需求，可交付给测试用户。