using System;
using LAN_Fight.Network;
using LAN_Fight.GameLogic;

namespace LAN_Fight
{
    /// <summary>
    /// 全局静态容器，定义常量、全局状态、以及对核心管理器的静态引用
    /// </summary>
    public static class ModGlobals
    {
        #region 常量定义
        
        /// <summary>
        /// 模组版本
        /// </summary>
        public const string MOD_VERSION = "1.0.0";
        
        /// <summary>
        /// 默认网络端口
        /// </summary>
        public const int DEFAULT_PORT = 7777;
        
        /// <summary>
        /// 最大客户端数量
        /// </summary>
        public const int MAX_CLIENTS = 7;
        
        /// <summary>
        /// 网络更新频率（毫秒）
        /// </summary>
        public const int NETWORK_UPDATE_INTERVAL = 16; // ~60 FPS
        
        #endregion

        #region 全局状态
        
        /// <summary>
        /// 是否为服务器模式
        /// </summary>
        public static bool IsServer { get; set; } = false;
        
        /// <summary>
        /// 是否为客户端模式
        /// </summary>
        public static bool IsClient { get; set; } = false;
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized { get; private set; } = false;
        
        /// <summary>
        /// 当前连接状态
        /// </summary>
        public static ConnectionState CurrentConnectionState { get; set; } = ConnectionState.Disconnected;
        
        #endregion

        #region 核心管理器引用
        
        /// <summary>
        /// 网络管理器
        /// </summary>
        public static NetworkManager NetworkManager { get; private set; }
        
        /// <summary>
        /// 副将管理器
        /// </summary>
        public static CoopHeroManager HeroManager { get; private set; }
        
        /// <summary>
        /// 指派管理器
        /// </summary>
        public static AssignmentManager AssignmentManager { get; private set; }
        
        #endregion

        #region 初始化和清理
        
        /// <summary>
        /// 初始化所有管理器
        /// </summary>
        public static void Initialize()
        {
            if (IsInitialized)
                return;
                
            try
            {
                // 初始化网络管理器
                NetworkManager = new NetworkManager();
                
                // 初始化游戏逻辑管理器
                HeroManager = new CoopHeroManager();
                AssignmentManager = new AssignmentManager();
                
                IsInitialized = true;
            }
            catch (Exception ex)
            {
                throw new Exception($"ModGlobals初始化失败: {ex.Message}", ex);
            }
        }
        
        /// <summary>
        /// 清理所有资源
        /// </summary>
        public static void Cleanup()
        {
            if (!IsInitialized)
                return;
                
            try
            {
                // 清理网络连接
                NetworkManager?.Dispose();
                NetworkManager = null;
                
                // 清理游戏逻辑管理器
                HeroManager?.Dispose();
                HeroManager = null;
                
                AssignmentManager?.Dispose();
                AssignmentManager = null;
                
                // 重置状态
                IsServer = false;
                IsClient = false;
                CurrentConnectionState = ConnectionState.Disconnected;
                IsInitialized = false;
            }
            catch (Exception ex)
            {
                // 清理时的异常不应该阻止程序继续运行
                System.Diagnostics.Debug.WriteLine($"ModGlobals清理时出错: {ex.Message}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 连接状态枚举
    /// </summary>
    public enum ConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        Hosting
    }
}
