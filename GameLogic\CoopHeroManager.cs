using System;
using System.Collections.Generic;
using System.Linq;
using TaleWorlds.CampaignSystem;
using TaleWorlds.Core;
using TaleWorlds.Library;

namespace LAN_Fight.GameLogic
{
    /// <summary>
    /// 副将NPC数据库，负责创建、存储和查询"联机副将"
    /// </summary>
    public class CoopHeroManager : IDisposable
    {
        #region 私有字段
        
        private readonly Dictionary<string, Hero> _coopHeroes;
        private readonly Dictionary<string, string> _heroPlayerMapping; // HeroId -> PlayerId
        private bool _isDisposed = false;
        
        #endregion

        #region 事件
        
        /// <summary>
        /// 副将创建事件
        /// </summary>
        public event Action<Hero> OnHeroCreated;
        
        /// <summary>
        /// 副将删除事件
        /// </summary>
        public event Action<string> OnHeroRemoved;
        
        #endregion

        #region 属性
        
        /// <summary>
        /// 所有联机副将
        /// </summary>
        public IReadOnlyDictionary<string, Hero> CoopHeroes => _coopHeroes;
        
        /// <summary>
        /// 副将数量
        /// </summary>
        public int HeroCount => _coopHeroes.Count;
        
        /// <summary>
        /// 可用的副将（未被指派的）
        /// </summary>
        public IEnumerable<Hero> AvailableHeroes => _coopHeroes.Values.Where(h => !_heroPlayerMapping.ContainsKey(h.StringId));
        
        #endregion

        #region 构造函数
        
        public CoopHeroManager()
        {
            _coopHeroes = new Dictionary<string, Hero>();
            _heroPlayerMapping = new Dictionary<string, string>();
        }
        
        #endregion

        #region 副将管理方法
        
        /// <summary>
        /// 创建一个新的联机副将
        /// </summary>
        /// <param name="name">副将名称</param>
        /// <param name="culture">文化</param>
        /// <returns>创建的副将，如果失败返回null</returns>
        public Hero CreateCoopHero(string name, CultureObject culture = null)
        {
            if (string.IsNullOrEmpty(name))
                return null;
                
            try
            {
                // 检查是否已存在同名副将
                if (_coopHeroes.Values.Any(h => h.Name?.ToString() == name))
                {
                    InformationManager.DisplayMessage(new InformationMessage($"已存在名为'{name}'的副将", Colors.Yellow));
                    return null;
                }
                
                // 使用默认文化（如果未指定）
                if (culture == null)
                {
                    culture = Game.Current?.ObjectManager?.GetObjectTypeList<CultureObject>()?.FirstOrDefault();
                }
                
                if (culture == null)
                {
                    InformationManager.DisplayMessage(new InformationMessage("无法获取文化信息，副将创建失败", Colors.Red));
                    return null;
                }
                
                // 创建副将
                // 注意：这里需要根据实际的Bannerlord API来创建Hero
                // 这是一个简化的实现，实际可能需要更复杂的逻辑
                
                var heroId = $"coop_hero_{Guid.NewGuid():N}";
                
                // TODO: 实现真正的Hero创建逻辑
                // Hero hero = HeroCreator.CreateSpecialHero(...);
                
                // 暂时返回null，等待实际实现
                Hero hero = null;
                
                if (hero != null)
                {
                    _coopHeroes[heroId] = hero;
                    OnHeroCreated?.Invoke(hero);
                    
                    InformationManager.DisplayMessage(new InformationMessage($"副将'{name}'创建成功", Colors.Green));
                }
                
                return hero;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"创建副将时出错: {ex.Message}", Colors.Red));
                return null;
            }
        }
        
        /// <summary>
        /// 删除指定的副将
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveCoopHero(string heroId)
        {
            if (string.IsNullOrEmpty(heroId))
                return false;
                
            try
            {
                if (!_coopHeroes.ContainsKey(heroId))
                    return false;
                
                var hero = _coopHeroes[heroId];
                
                // 如果副将被指派给玩家，先取消指派
                if (_heroPlayerMapping.ContainsKey(heroId))
                {
                    _heroPlayerMapping.Remove(heroId);
                }
                
                // 从游戏中移除副将
                // TODO: 实现真正的Hero移除逻辑
                
                _coopHeroes.Remove(heroId);
                OnHeroRemoved?.Invoke(heroId);
                
                InformationManager.DisplayMessage(new InformationMessage($"副将'{hero.Name}'已删除", Colors.Yellow));
                return true;
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"删除副将时出错: {ex.Message}", Colors.Red));
                return false;
            }
        }
        
        /// <summary>
        /// 获取指定ID的副将
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <returns>副将对象，如果不存在返回null</returns>
        public Hero GetCoopHero(string heroId)
        {
            if (string.IsNullOrEmpty(heroId))
                return null;
                
            _coopHeroes.TryGetValue(heroId, out Hero hero);
            return hero;
        }
        
        /// <summary>
        /// 检查指定Hero是否为联机副将
        /// </summary>
        /// <param name="hero">要检查的Hero</param>
        /// <returns>是否为联机副将</returns>
        public bool IsCoopHero(Hero hero)
        {
            if (hero == null)
                return false;
                
            return _coopHeroes.ContainsValue(hero);
        }
        
        /// <summary>
        /// 获取副将的ID
        /// </summary>
        /// <param name="hero">副将对象</param>
        /// <returns>副将ID，如果不是联机副将返回null</returns>
        public string GetHeroId(Hero hero)
        {
            if (hero == null)
                return null;
                
            return _coopHeroes.FirstOrDefault(kvp => kvp.Value == hero).Key;
        }
        
        #endregion

        #region 指派相关方法
        
        /// <summary>
        /// 将副将指派给玩家
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <param name="playerId">玩家ID</param>
        /// <returns>是否指派成功</returns>
        public bool AssignHeroToPlayer(string heroId, string playerId)
        {
            if (string.IsNullOrEmpty(heroId) || string.IsNullOrEmpty(playerId))
                return false;
                
            if (!_coopHeroes.ContainsKey(heroId))
                return false;
                
            // 如果副将已被指派，先取消原指派
            if (_heroPlayerMapping.ContainsKey(heroId))
            {
                _heroPlayerMapping.Remove(heroId);
            }
            
            _heroPlayerMapping[heroId] = playerId;
            return true;
        }
        
        /// <summary>
        /// 取消副将的玩家指派
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <returns>是否取消成功</returns>
        public bool UnassignHero(string heroId)
        {
            if (string.IsNullOrEmpty(heroId))
                return false;
                
            return _heroPlayerMapping.Remove(heroId);
        }
        
        /// <summary>
        /// 获取副将被指派给的玩家ID
        /// </summary>
        /// <param name="heroId">副将ID</param>
        /// <returns>玩家ID，如果未被指派返回null</returns>
        public string GetAssignedPlayer(string heroId)
        {
            if (string.IsNullOrEmpty(heroId))
                return null;
                
            _heroPlayerMapping.TryGetValue(heroId, out string playerId);
            return playerId;
        }
        
        /// <summary>
        /// 获取玩家被指派的副将ID
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>副将ID，如果未被指派返回null</returns>
        public string GetPlayerAssignedHero(string playerId)
        {
            if (string.IsNullOrEmpty(playerId))
                return null;
                
            return _heroPlayerMapping.FirstOrDefault(kvp => kvp.Value == playerId).Key;
        }
        
        #endregion

        #region IDisposable实现
        
        public void Dispose()
        {
            if (_isDisposed)
                return;
                
            try
            {
                // 清理所有副将
                var heroIds = _coopHeroes.Keys.ToList();
                foreach (var heroId in heroIds)
                {
                    RemoveCoopHero(heroId);
                }
                
                _coopHeroes.Clear();
                _heroPlayerMapping.Clear();
                
                _isDisposed = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CoopHeroManager释放资源时出错: {ex.Message}");
            }
        }
        
        #endregion
    }
}
