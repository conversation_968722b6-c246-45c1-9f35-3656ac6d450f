using System.Collections.Generic;
using TaleWorlds.Library;
using static TaleWorlds.Library.CommandLineFunctionality;

namespace LAN_Fight
{
    /// <summary>
    /// LAN Fight 控制台命令 - 使用属性注册
    /// 提供基础的测试和状态命令
    /// </summary>
    public static class LANFightCommands
    {
        /// <summary>
        /// 显示帮助信息
        /// 使用方法: help.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("help", "lan_fight")]
        public static string ShowHelp(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 命令帮助 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage("help.lan_fight - 显示此帮助", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("test.lan_fight - 测试模组功能", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("info.lan_fight - 显示模组信息", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("version.lan_fight - 显示版本信息", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("clear.lan_fight - 清理消息", Colors.White));
                
                return "LAN Fight 帮助信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"显示帮助时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试模组功能
        /// 使用方法: test.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("test", "lan_fight")]
        public static string TestMod(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组测试 ===", Colors.Yellow));
                InformationManager.DisplayMessage(new InformationMessage("✓ 模组已正确加载", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("✓ 控制台命令系统正常", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("✓ 消息显示系统正常", Colors.Green));
                InformationManager.DisplayMessage(new InformationMessage("✓ 属性注册系统正常", Colors.Green));
                
                return "模组测试完成 - 所有基础功能正常";
            }
            catch (System.Exception ex)
            {
                return $"测试时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示模组详细信息
        /// 使用方法: info.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("info", "lan_fight")]
        public static string ShowModInfo(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 模组信息 ===", Colors.Cyan));
                InformationManager.DisplayMessage(new InformationMessage($"版本: {SubModule.MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("类型: 局域网多人联机模组", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("状态: 基础版本", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("作者: LAN_Fight开发团队", Colors.White));
                
                return "模组信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"显示模组信息时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示版本信息
        /// 使用方法: version.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("version", "lan_fight")]
        public static string ShowVersion(List<string> args)
        {
            try
            {
                string versionInfo = $"LAN Fight v{SubModule.MOD_VERSION}";
                InformationManager.DisplayMessage(new InformationMessage(versionInfo, Colors.Green));
                
                return versionInfo;
            }
            catch (System.Exception ex)
            {
                return $"显示版本时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 清屏命令
        /// 使用方法: clear.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("clear", "lan_fight")]
        public static string ClearMessages(List<string> args)
        {
            try
            {
                // 显示多个空行来"清屏"
                for (int i = 0; i < 10; i++)
                {
                    InformationManager.DisplayMessage(new InformationMessage("", Colors.White));
                }
                
                InformationManager.DisplayMessage(new InformationMessage("=== 消息已清理 ===", Colors.Green));
                
                return "消息已清理";
            }
            catch (System.Exception ex)
            {
                return $"清理消息时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 调试命令 - 显示详细的系统信息
        /// 使用方法: debug.lan_fight
        /// </summary>
        [CommandLineArgumentFunction("debug", "lan_fight")]
        public static string DebugInfo(List<string> args)
        {
            try
            {
                InformationManager.DisplayMessage(new InformationMessage("=== LAN Fight 调试信息 ===", Colors.Magenta));
                InformationManager.DisplayMessage(new InformationMessage($"模组版本: {SubModule.MOD_VERSION}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"游戏版本: {ApplicationVersion.FromParametersFile().ToString()}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage($"当前时间: {System.DateTime.Now}", Colors.White));
                InformationManager.DisplayMessage(new InformationMessage("命令系统: 正常运行", Colors.Green));
                
                return "调试信息已显示";
            }
            catch (System.Exception ex)
            {
                return $"显示调试信息时出错: {ex.Message}";
            }
        }
    }
}
