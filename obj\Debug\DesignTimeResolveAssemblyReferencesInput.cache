   .winmd.dll.exe    'D:\VS Project\LAN_Fight\packages.config(D:\VS Project\LAN_Fight\lib\0Harmony.dllKD:\VS Project\LAN_Fight\packages\LiteNetLib.1.3.1\lib\net471\LiteNetLib.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Microsoft.CSharp.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\mscorlib.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}"D:\VS Project\LAN_Fight\bin\Debug\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}KD:\VS Project\LAN_Fight\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2msil
v4.0.30319         