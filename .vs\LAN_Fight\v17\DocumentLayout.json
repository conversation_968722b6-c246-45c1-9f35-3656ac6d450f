{"Version": 1, "WorkspaceRootPath": "D:\\VS Project\\LAN_Fight\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\VS Project\\LAN_Fight\\LAN_Fight_Mod\\SubModule.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:LAN_Fight_Mod\\SubModule.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{A3EB48EA-3AF6-4744-BD60-593608D42EC6}|LAN_Fight.csproj|d:\\vs project\\lan_fight\\submodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A3EB48EA-3AF6-4744-BD60-593608D42EC6}|LAN_Fight.csproj|solutionrelative:submodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "SubModule.xml", "DocumentMoniker": "D:\\VS Project\\LAN_Fight\\LAN_Fight_Mod\\SubModule.xml", "RelativeDocumentMoniker": "LAN_Fight_Mod\\SubModule.xml", "ToolTip": "D:\\VS Project\\LAN_Fight\\LAN_Fight_Mod\\SubModule.xml", "RelativeToolTip": "LAN_Fight_Mod\\SubModule.xml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-11T15:46:49.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SubModule.cs", "DocumentMoniker": "D:\\VS Project\\LAN_Fight\\SubModule.cs", "RelativeDocumentMoniker": "SubModule.cs", "ToolTip": "D:\\VS Project\\LAN_Fight\\SubModule.cs", "RelativeToolTip": "SubModule.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T13:23:43.22Z", "EditorCaption": ""}]}]}]}